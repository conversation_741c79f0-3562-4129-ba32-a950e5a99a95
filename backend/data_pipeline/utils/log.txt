[2025-07-29 14:34:07][uvicorn.error.startup():62] [INFO] Application startup complete.
projectType =  EP2002
projectName =  V3
start_time =  2025-03-16T03:20:00Z
end_time =  2025-03-16T03:29:25Z
adjusted_start =  2025-03-16T11:20:00Z
adjusted_end =  2025-03-16T11:29:25Z
node_type =  RBX-logical
node_name =  RBX L1A
influx_ubique_tagId =  7d9sc
signal_name =  ASP 1
nodeType_nodeName =  RBX-logical_RBX L1A
执行的查询语句:

            from(bucket: "EP2002")
                |> range(start: 2025-03-16T11:20:00Z, stop: 2025-03-16T11:29:25Z)
                |> filter(fn: (r) => r["_measurement"] == "V3")
                |> filter(fn: (r) => r["nodeType_nodeName"] == "RBX-logical_RBX L1A")
                |> filter(fn: (r) => r["influx_ubique_tagId"] == "7d9sc")
                |> filter(fn: (r) => r["_field"] == "ASP 1")
                |> yield(name: "mean")
            
查询执行完成，开始处理结果...
处理表格，记录数: 401
已处理 100 条记录
已处理 200 条记录
已处理 300 条记录
已处理 400 条记录
查询到的数据点数组长度: 401
数据处理完成，准备返回...
开始关闭InfluxDB连接...
关闭线程池...
关闭写入API...
关闭客户端...
InfluxDB连接关闭完成
开始关闭InfluxDB连接...
关闭线程池...
关闭写入API...
关闭客户端...
InfluxDB连接关闭完成
[2025-07-29 14:34:15][django.request.log_response():253] [ERROR] Internal Server Error: /api/data_pipeline/file/influxdb/query/
[2025-07-29 14:34:15][uvicorn.access.send():466] [INFO] 127.0.0.1:61596 - "POST /api/data_pipeline/file/influxdb/query/ HTTP/1.1" 500
[2025-07-29 14:34:15][uvicorn.access.send():466] [INFO] 127.0.0.1:61596 - "POST /api/data_pipeline/file/influxdb/query/ HTTP/1.1" 500