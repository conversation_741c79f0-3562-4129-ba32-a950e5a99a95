#!/usr/bin/env python3
"""
测试InfluxDB连接的脚本
"""

import sys
import os

# 添加Django项目路径
sys.path.append('/Users/<USER>/Desktop/datapiPeline/metisgrid/backend')

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings')

import django
django.setup()

from django.conf import settings
from influxdb_client import InfluxDBClient
from influxdb_client.client.exceptions import InfluxDBError
import requests

def test_influxdb_connection():
    """测试InfluxDB连接"""
    
    print("=== InfluxDB连接测试 ===")
    print(f"URL: {settings.INFLUXDB_CONFIG['url']}")
    print(f"ORG: {settings.INFLUXDB_CONFIG['org']}")
    print(f"Bucket: {settings.INFLUXDB_CONFIG['bucket']}")
    print(f"Token: {settings.INFLUXDB_CONFIG['token'][:20]}...")
    
    # 1. 测试HTTP连接
    print("\n1. 测试HTTP连接...")
    try:
        response = requests.get(f"{settings.INFLUXDB_CONFIG['url']}/health", timeout=10)
        print(f"   HTTP状态码: {response.status_code}")
        if response.status_code == 200:
            print("   ✓ HTTP连接正常")
        else:
            print(f"   ✗ HTTP连接异常: {response.text}")
    except Exception as e:
        print(f"   ✗ HTTP连接失败: {e}")
        return False
    
    # 2. 测试InfluxDB客户端连接
    print("\n2. 测试InfluxDB客户端连接...")
    try:
        client = InfluxDBClient(
            url=settings.INFLUXDB_CONFIG['url'],
            token=settings.INFLUXDB_CONFIG['token'],
            org=settings.INFLUXDB_CONFIG['org'],
            timeout=10_000
        )
        
        # 测试ping
        health = client.health()
        print(f"   InfluxDB健康状态: {health.status}")
        if health.status == "pass":
            print("   ✓ InfluxDB服务正常")
        else:
            print(f"   ✗ InfluxDB服务异常: {health}")
            
    except InfluxDBError as e:
        print(f"   ✗ InfluxDB连接错误: {e}")
        return False
    except Exception as e:
        print(f"   ✗ 连接失败: {e}")
        return False
    
    # 3. 测试组织和bucket
    print("\n3. 测试组织和bucket...")
    try:
        # 获取组织信息
        orgs_api = client.organizations_api()
        orgs = orgs_api.find_organizations()
        org_names = [org.name for org in orgs]
        print(f"   可用组织: {org_names}")
        
        if settings.INFLUXDB_CONFIG['org'] in org_names:
            print(f"   ✓ 组织 '{settings.INFLUXDB_CONFIG['org']}' 存在")
        else:
            print(f"   ✗ 组织 '{settings.INFLUXDB_CONFIG['org']}' 不存在")
            
        # 获取bucket信息
        buckets_api = client.buckets_api()
        buckets = buckets_api.find_buckets()
        bucket_names = [bucket.name for bucket in buckets]
        print(f"   可用bucket: {bucket_names}")
        
        if settings.INFLUXDB_CONFIG['bucket'] in bucket_names:
            print(f"   ✓ Bucket '{settings.INFLUXDB_CONFIG['bucket']}' 存在")
        else:
            print(f"   ✗ Bucket '{settings.INFLUXDB_CONFIG['bucket']}' 不存在")
            
    except Exception as e:
        print(f"   ✗ 获取组织/bucket信息失败: {e}")
        return False
    
    # 4. 测试简单查询
    print("\n4. 测试简单查询...")
    try:
        query_api = client.query_api()
        # 简单的健康检查查询
        query = f'''
        from(bucket: "{settings.INFLUXDB_CONFIG['bucket']}")
            |> range(start: -1h)
            |> limit(n: 1)
        '''
        result = query_api.query(query, org=settings.INFLUXDB_CONFIG['org'])
        print("   ✓ 查询执行成功")
        
    except Exception as e:
        print(f"   ✗ 查询失败: {e}")
        # 查询失败不一定是连接问题，可能是没有数据
    
    finally:
        client.close()
    
    print("\n=== 测试完成 ===")
    return True

def test_token_permissions():
    """测试token权限"""
    print("\n=== Token权限测试 ===")
    
    try:
        client = InfluxDBClient(
            url=settings.INFLUXDB_CONFIG['url'],
            token=settings.INFLUXDB_CONFIG['token'],
            org=settings.INFLUXDB_CONFIG['org'],
            timeout=10_000
        )
        
        # 测试读权限
        print("测试读权限...")
        query_api = client.query_api()
        query = f'buckets() |> filter(fn: (r) => r.name == "{settings.INFLUXDB_CONFIG["bucket"]}")'
        result = query_api.query(query)
        print("   ✓ 读权限正常")
        
        # 测试写权限
        print("测试写权限...")
        from influxdb_client import Point, WritePrecision
        write_api = client.write_api()
        
        point = Point("test_measurement") \
            .tag("test_tag", "test_value") \
            .field("test_field", 1.0) \
            .time(1234567890, WritePrecision.S)
            
        write_api.write(bucket=settings.INFLUXDB_CONFIG['bucket'], 
                       org=settings.INFLUXDB_CONFIG['org'], 
                       record=point)
        print("   ✓ 写权限正常")
        
        client.close()
        
    except Exception as e:
        print(f"   ✗ Token权限测试失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = test_influxdb_connection()
    if success:
        test_token_permissions()
