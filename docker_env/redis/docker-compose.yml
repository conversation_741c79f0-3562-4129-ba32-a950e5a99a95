version: '3.8'

services:
  redis:
    image: redis:latest
    container_name: dataPipeline_redis
    environment:
      - REDIS_PORT=6379
      # 设置 Redis 密码（可选，建议生产环境开启）
      - REDIS_PASSWORD=admin123456
    ports:
      - "6379:6379"  # 映射 Redis 默认端口
    volumes:
      - redis_data:/data  # Redis 持久化数据目录
      - ./redis.conf:/usr/local/etc/redis/redis.conf  # 挂载自定义配置文件（可选）
    command:
      - redis-server
      - /usr/local/etc/redis/redis.conf  # 使用自定义配置文件启动
    networks:
      - pub-network
    restart: always

# 定义持久化卷
volumes:
  redis_data:

# 使用已有网络（pub-network）
networks:
  pub-network:
    external: true